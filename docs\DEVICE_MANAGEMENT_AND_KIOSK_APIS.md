# Device Management and Kiosk APIs Documentation

## Overview

This document provides comprehensive documentation for the device management system and kiosk APIs, covering the complete flow from admin panel device management to kiosk authentication and NDA signing processes.

## Device Management (Admin Panel)

### Device Model Structure

The Device model includes the following key fields:
- `device_id`: Primary key (UUID, auto-generated)
- `identifier`: Unique device identifier (UUID, auto-generated) - used for QR codes
- `name`: Device display name
- `fingerprint_data`: Stores device fingerprint after authentication
- `qr_code`: Generated QR code image (base64)
- `kiosk_group_id`: Associated kiosk group
- `facility_id`, `building_id`, `floor_id`, `room_id`: Location hierarchy

### Device Creation Flow

1. **Admin creates device** via `/api/devices` endpoint
2. **Auto-generation**: <PERSON>ce gets auto-generated UUID for `identifier` field
3. **QR Code generation**: System automatically generates QR code using the `identifier`
4. **QR Code content**: `{server_url}/api/kiosk/device/authenticate/{identifier}`

### Device APIs

#### Create Device
```
POST /api/devices
Authorization: Bearer {admin_token}

Body:
{
  "name": "Main Lobby Kiosk 1",
  "kiosk_group_id": "uuid",
  "facility_id": "uuid",
  "building_id": "uuid",
  "floor_id": "uuid",
  "room_id": "uuid"
}
```

#### Get Device Details
```
GET /api/devices/{facilityId}/{deviceId}
Authorization: Bearer {admin_token}
```

#### Update Device (with QR regeneration)
```
PUT /api/devices/{facilityId}/{deviceId}
Authorization: Bearer {admin_token}

Body:
{
  "name": "Updated Device Name",
  "generate_qr": true  // Optional: regenerate QR code
}
```

## QR Code Scanning and Device Authentication Flow

### Step 1: QR Code Scanning
- Admin panel displays QR code for each device
- Mobile app scans QR code containing: `/api/kiosk/device/authenticate/{identifier}`
- Mobile app extracts the `identifier` from the URL

### Step 2: Device Authentication
```
POST /api/kiosk/device/authenticate/{identifier}
Content-Type: application/json

Body:
{
  "fingerprint_data": "device_hardware_signature_hash"
}

Response:
{
  "status": "success",
  "message": "Device authenticated successfully",
  "data": {
    "access_token": "jwt_token",
    "refresh_token": "jwt_token", 
    "device_config": {
      "device_id": "uuid",
      "name": "Device Name",
      "identifier": "uuid",
      "kiosk_group": { "name": "Group Name" },
      "facility": { "name": "Facility Name" }
    },
    "permissions": ["access_kiosk", ...]
  }
}
```

### Authentication Process:
1. Validates `identifier` exists in Device table
2. Stores `fingerprint_data` in device record
3. Returns kiosk user tokens (<EMAIL>)
4. Provides device configuration and permissions

## Kiosk APIs

All kiosk APIs require authentication with the token received from device authentication.

### Device Settings
```
GET /api/kiosk/device/{device_identifier}/settings
Authorization: Bearer {kiosk_token}

Response: Device-specific kiosk settings and NDA templates
```

### Guest Management

#### Fetch Guest by PIN
```
POST /api/kiosk/guest/fetch-by-pin
Authorization: Bearer {kiosk_token}

Body:
{
  "device_id": "device_identifier",
  "guest_pin": "1234",
  "guest_name": "John Doe"
}
```

#### Patient Verification
```
POST /api/kiosk/patient/verify-appointment
Authorization: Bearer {kiosk_token}

Body:
{
  "device_id": "device_identifier", 
  "appointment_guest_id": "uuid",
  "phone_last_4": "1234"
}
```

### Check-in Process
```
POST /api/kiosk/checkin/appointment-guest
Authorization: Bearer {kiosk_token}

Body:
{
  "appointment_guest_id": "uuid",
  "device_id": "device_identifier",
  "checkin_time": "2024-01-01T10:00:00Z"
}
```

## NDA Management and Signing Process

### Get NDA Template
```
GET /api/kiosk/device/{nda_template_id}/templates
Authorization: Bearer {kiosk_token}

Response:
{
  "status": "success",
  "data": {
    "nda_template_id": "uuid",
    "title": "NDA Agreement",
    "content": "NDA content...",
    "version": "1.0",
    "effective_date": "2024-01-01",
    "jurisdiction": "State/Country"
  }
}
```

### Store Guest Signature
```
POST /api/kiosk/signature/store
Authorization: Bearer {kiosk_token}
Content-Type: multipart/form-data

Body:
{
  "appointment_guest_id": "uuid",  // OR patient_id
  "nda_template_id": "uuid",
  "signature": "base64_signature_image",
  "updated_by": "uuid"
}
```

### NDA Signing Flow:
1. **Display NDA**: Get template content via template API
2. **Collect Signature**: User signs on device screen
3. **Store Signature**: Submit signature via store API
4. **Complete Process**: System creates NDA agreement record

## Authentication Credentials

### Admin Panel Testing
- Email: `<EMAIL>`
- Password: `Pa$w0rd!`

### Kiosk Authentication
- Email: `<EMAIL>` (automatic)
- Password: `Pa$w0rd!` (automatic)

## Error Handling

All APIs return standardized error responses:
```json
{
  "status": "error",
  "message": "Error description",
  "code": 400
}
```

Common error scenarios:
- Invalid device identifier
- Device not found
- Missing fingerprint data
- Invalid guest PIN
- NDA template not found
- Signature upload failure

## Security Considerations

1. **Device Authentication**: Each device must authenticate before accessing kiosk APIs
2. **Fingerprint Storage**: Device fingerprints are stored for security tracking
3. **Token Management**: Kiosk tokens have limited scope and permissions
4. **NDA Compliance**: All signatures are stored with audit trails
5. **Data Validation**: All inputs are validated using Joi schemas

## Integration Notes

- QR codes contain device identifiers, not device IDs
- Device fingerprinting occurs during first authentication
- Kiosk user permissions are embedded in JWT tokens
- NDA templates are associated with kiosk groups
- All operations support transaction rollback for data consistency

## Important Parameter Naming

**Note**: In kiosk APIs, the parameter `device_id` in request bodies and some route parameters actually refers to the device `identifier` field, not the primary key `device_id`. This is for backward compatibility:

- Route parameter `/device/:device_id/setting` → `device_id` is actually the `identifier`
- Request body field `device_id` → refers to the device `identifier`
- Authentication route `/device/authenticate/:identifier` → correctly uses `identifier`

The system internally maps these parameters to the `identifier` field for device lookups.
