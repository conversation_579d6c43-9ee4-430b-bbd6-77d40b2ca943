const Joi = require("joi");
const { exists, unique } = require("./custom.validation");

// Reusable identifiers
const kioskGroupId = Joi.string()
  .required()
  .external(exists("KioskGroup", "kiosk_group_id"));
const kioskSettingId = Joi.string()
  .required()
  .external(exists("KioskSetting", "kiosk_setting_id"));
const kioskGroupSettingId = Joi.string()
  .required()
  .external(exists("KioskGroupSetting", "kiosk_group_setting_id"));

const kioskGroup = {
  params: Joi.object().keys({
    kioskGroupId,
  }),
};

const create = {
  params: Joi.object().keys({
    kioskGroupId,
  }),
  body: Joi.object()
    .keys({
      kiosk_setting_id: kioskSettingId,
      config_value: Joi.string().optional(),
    })
    .external(unique("KioskGroupSetting", ["kiosk_group_id", "kiosk_setting_id"])),
};

const kioskGroupSetting = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
};

const update = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
  body: Joi.object()
    .keys({
      config_value: Joi.string().optional(),
    })
    .min(1),
};

const remove = {
  params: Joi.object().keys({
    kioskGroupId,
    kioskGroupSettingId,
  }),
};

module.exports = {
  kioskGroup,
  create,
  kioskGroupSetting,
  update,
  remove,
};
