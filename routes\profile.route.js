const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const { ProfileController } = require("../controllers");
const { ProfileValidation } = require("../validations");
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Profile
 *   description: User profile management
 */

/**
 * @swagger
 * /profile/myProfile:
 *   get:
 *     summary: Get current user profile details
 *     description: Retrieves the profile information of the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [Profile]
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User profile retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     identity_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                     first_name:
 *                       type: string
 *                       example: "<PERSON>"
 *                     last_name:
 *                       type: string
 *                       example: "Doe"
 *                     middle_name:
 *                       type: string
 *                       example: "Michael"
 *                     eid:
 *                       type: string
 *                       example: "EMP001"
 *                     identity_type:
 *                       type: integer
 *                       example: 1
 *                     identity_type_name:
 *                       type: string
 *                       example: "Employee"
 *                     national_id:
 *                       type: string
 *                       example: "123456789"
 *                     mobile:
 *                       type: string
 *                       example: "+1234567890"
 *                     start_date:
 *                       type: string
 *                       format: date
 *                       example: "2023-01-01"
 *                     end_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-12-31"
 *                     status:
 *                       type: integer
 *                       example: 1
 *                     status_name:
 *                       type: string
 *                       example: "Active"
 *                     suspension:
 *                       type: boolean
 *                       example: false
 *                     suspension_date:
 *                       type: string
 *                       format: date
 *                       example: null
 *                     reason:
 *                       type: string
 *                       example: null
 *                     image:
 *                       type: string
 *                       example: "profile_image_url.jpg"
 *                     company:
 *                       type: string
 *                       example: "CareMate Inc."
 *                     organization:
 *                       type: string
 *                       example: "IT Department"
 *                     company_code:
 *                       type: string
 *                       example: "CM001"
 *                     job_title:
 *                       type: string
 *                       example: "Software Engineer"
 *                     job_code:
 *                       type: string
 *                       example: "SE001"
 *                     manager:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     facility:
 *                       type: object
 *                       properties:
 *                         facility_id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                         address:
 *                           type: object
 *                           properties:
 *                             address_line_1:
 *                               type: string
 *                             address_line_2:
 *                               type: string
 *                             postal_code:
 *                               type: string
 *                             region:
 *                               type: string
 *                             country:
 *                               type: string
 *                             state:
 *                               type: string
 *                     language_preference:
 *                       type: object
 *                       properties:
 *                         language_id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         code:
 *                           type: string
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: User profile not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User profile not found"
 */
router.get(
  "/myProfile",
  auth(),
  ProfileController.getCurrentUserProfile
);

/**
 * @swagger
 * /profile/language:
 *   put:
 *     summary: Update user language preference
 *     security:
 *       - bearerAuth: []
 *     tags: [Profile]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language_id:
 *                 type: string
 *                 description: The ID of the language to set as preference.
 *     responses:
 *       200:
 *         description: Language preference updated successfully.
 *         content:
 *           application/json:
 *             example:
 *               message: "Language preference updated successfully."
 */
router.put(
  "/language",
  auth(),
  validate(ProfileValidation.language),
  ProfileController.language
);

module.exports = router;
