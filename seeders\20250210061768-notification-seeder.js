"use strict";
const { v4: uuidv4 } = require("uuid");

module.exports = {
  up: async (queryInterface, Sequelize) => {
    await queryInterface.bulkInsert('notification', [
      {
        notification_id: uuidv4(),
        name: 'Send Check In notification',
        schema: 'PatientAppointmentGuestView',
        schema_column: 'appointment_guest_id',
        description: 'Send check-in notification to patient on his/her guest arrival',
        status: 'Active',
        language: 'en-US',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_id: uuidv4(),
        name: 'Identity Created Notification',
        schema: 'Identity',
        schema_column: 'identity_id',
        description: 'Send notification when a new identity is created',
        status: 'Active',
        language: 'en-US',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_id: uuidv4(),
        name: 'Identity Updated Notification',
        schema: 'Identity',
        schema_column: 'identity_id',
        description: 'Send notification when an identity is updated',
        status: 'Active',
        language: 'en-US',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_id: uuidv4(),
        name: 'Inpatient Admission Notification',
        schema: 'PatientAppointmentExtendedView',
        schema_column: 'appointment_id',
        description: 'Send notification when an inpatient admitted',
        status: 'Active',
        language: 'en-US',
        created_at: new Date(),
        updated_at: new Date(),
      },
      {
        notification_id: uuidv4(),
        name: 'Send Visit notification',
        schema: 'VisitExtendedView',
        schema_column: 'visit_id',
        description: 'Send notification when an visit is created',
        status: 'Active',
        language: 'en-US',
        created_at: new Date(),
        updated_at: new Date(),
      },
    ]);
  },

  down: async (queryInterface, Sequelize) => {
    await queryInterface.bulkDelete('notification', null, {});
  },
};