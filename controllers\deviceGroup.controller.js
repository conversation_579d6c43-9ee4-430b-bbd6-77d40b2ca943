const { KioskGroupSetting, KioskGroup, KioskSetting, MasterData, NdaTemplate } = require("../models");
const { paginate } = require("../models/plugins/paginate.plugin");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");

/**
 * Get all kiosk settings (without pagination).
 *
 * @async
 * @function fetchKioskSettings
 * @param {Object} req - Express request object.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of kiosk setting data.
 */
exports.fetchKioskSettings = catchAsync(async (req, res, next) => {
  const kioskSettings = await KioskSetting.findAll({
    attributes: ["kiosk_setting_id", "config_key", "name", "description", "config_group", "default_config_value"],
    order: [['config_group', 'ASC'], ['name', 'ASC']],
  });

  sendSuccess(res, "Kiosk settings fetched successfully", httpStatus.OK, { data: kioskSettings });
});

/**
 * Get all kiosk group settings for a specific kiosk group (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the kioskGroupId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with kiosk group setting data, including kiosk setting details.
 */
exports.index = catchAsync(async (req, res) => {
  const { kioskGroupId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { kiosk_group_id: kioskGroupId },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: KioskSetting,
        as: "kiosk_setting",
        attributes: ["config_key", "name", "description", "config_group", "default_config_value"],
        include: [
          {
            model: MasterData,
            as: "masterdata",
            attributes: ["key", "value"],
            required: false,
          },
        ],
      },
    ],
  };

  const result = await paginate(KioskGroupSetting, queryOptions, paginationOptions);
  sendSuccess(res, "Device group settings retrieved successfully", httpStatus.OK, result);
});

/**
 * Get a single kiosk group setting by its ID for a specific kiosk group.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the kiosk group setting details with kiosk setting information or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res) => {
  const { kioskGroupId, kioskGroupSettingId } = req.params;
  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: { 
      kiosk_group_setting_id: kioskGroupSettingId, 
      kiosk_group_id: kioskGroupId 
    },
    include: [
      {
        model: KioskGroup,
        as: "kiosk_group",
        attributes: ["name"],
      },
      {
        model: KioskSetting,
        as: "kiosk_setting",
        attributes: ["config_key", "name", "description", "config_group", "default_config_value"],
        include: [
          {
            model: MasterData,
            as: "masterdata",
            attributes: ["key", "value"],
            required: false,
          },
        ],
      },
    ],
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  sendSuccess(res, "Device group setting retrieved successfully", httpStatus.OK, kioskGroupSetting);
});

/**
 * Create a new kiosk group setting for a specific kiosk group.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the kioskGroupId.
 * @param {Object} req.body - Contains the kiosk group setting data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created kiosk group setting data including kiosk setting details.
 */
exports.create = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId } = req.params;
  const { kiosk_setting_id, config_value } = req.body;

  // Get the default config value from kiosk setting if not provided
  let finalConfigValue = config_value;
  if (!finalConfigValue) {
    const kioskSetting = await KioskSetting.findByPk(kiosk_setting_id, {
      attributes: ["default_config_value"],
    });
    finalConfigValue = kioskSetting?.default_config_value || "";
  }

  // const kioskGroupSetting = 
  await KioskGroupSetting.create(
    {
      kiosk_group_id: kioskGroupId,
      kiosk_setting_id,
      config_value: finalConfigValue,
    },
    { transaction }
  );

  // // Fetch the created record with includes for response
  // const createdKioskGroupSetting = await KioskGroupSetting.findByPk(
  //   kioskGroupSetting.kiosk_group_setting_id,
  //   {
  //     include: [
  //       {
  //         model: KioskGroup,
  //         as: "kiosk_group",
  //         attributes: ["name"],
  //       },
  //       {
  //         model: KioskSetting,
  //         as: "kiosk_setting",
  //         attributes: ["config_key", "name", "description", "config_group", "default_config_value"],
  //       },
  //     ],
  //   }
  // );

  sendSuccess(res, "Device group setting created successfully", httpStatus.CREATED, 
    []
    // createdKioskGroupSetting
  );
});

/**
 * Update a kiosk group setting's details.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} req.body - Contains the updated kiosk group setting data.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the updated kiosk group setting data or a 404 error if not found.
 */
exports.update = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId, kioskGroupSettingId } = req.params;
  const { config_value } = req.body;

  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: { 
      kiosk_group_setting_id: kioskGroupSettingId, 
      kiosk_group_id: kioskGroupId 
    },
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  await kioskGroupSetting.update({ config_value }, { transaction });

  sendSuccess(res, "Device group setting updated successfully", httpStatus.OK, []);
});

/**
 * Delete a kiosk group setting by ID.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains kioskGroupId and kioskGroupSettingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success message or a 404 error if not found.
 */
exports.remove = catchAsync(async (req, res) => {
  const transaction = req.transaction;
  const { kioskGroupId, kioskGroupSettingId } = req.params;

  const kioskGroupSetting = await KioskGroupSetting.findOne({
    where: { 
      kiosk_group_setting_id: kioskGroupSettingId, 
      kiosk_group_id: kioskGroupId 
    },
  });

  if (!kioskGroupSetting) {
    return sendError(res, "Device group setting not found", httpStatus.BAD_REQUEST);
  }

  await kioskGroupSetting.destroy({ transaction });

  sendSuccess(res, "Device group setting deleted successfully", httpStatus.OK, {
    kiosk_group_setting_id: kioskGroupSettingId,
  });
});
