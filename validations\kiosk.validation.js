const Joi = require("joi");

const getDeviceSetting = {
  params: Joi.object().keys({
    device_id: Joi.string().required(), // This is actually the device identifier
  }),
};

const fetchGuestByPin = {
  body: Joi.object().keys({
    device_id: Joi.string().required(),
    guest_pin: Joi.string().required(),
    guest_name: Joi.string().required(),
  }),
};

const getDeviceTemplate = {
  params: Joi.object().keys({
    nda_template_id: Joi.string().uuid().required(),
  }),
};

const getPatientByAppointmentAndPhone = {
  body: Joi.object().keys({
    device_id: Joi.string().required(),
    appointment_guest_id: Joi.string().uuid().required(),
    phone_last_4: Joi.string().length(4).pattern(/^\d{4}$/).required(),
  }),
};

const getOutpatientDetails = {
  body: Joi.object().keys({
    device_id: Joi.string().required(),
    cellphone: Joi.string().pattern(/^[\+]?[1-9][\d\s\-\(\)\.]{0,20}$/).required(),
    birth_date: Joi.date().iso().required(),
  }),
};

const performPatientGuestCheckin = {
  body: Joi.object().keys({
    device_id: Joi.string().required(),
    appointment_id: Joi.string().uuid().required(),
    appointment_guest_id: Joi.string().uuid().required(),
  }),
};

const getInpatientAppointmentDetails = {
  body: Joi.object().keys({
    device_id: Joi.string().required(),
    facility_id: Joi.string().uuid().required(),
    phone_last_4: Joi.string().length(4).pattern(/^\d{4}$/).required(),
    first_name_first_3: Joi.string().min(3).max(3).pattern(/^[a-zA-Z]{3}$/).required(),
    last_name_first_3: Joi.string().min(3).max(3).pattern(/^[a-zA-Z]{3}$/).required(),
  }),
};

const storeGuestSignature = {
  body: Joi.object().keys({
    appointment_guest_id: Joi.string().uuid().optional(),
    patient_id: Joi.string().uuid().optional(),
    nda_template_id: Joi.string().uuid().required(),
    signature: Joi.string().required(), // Base64 encoded image from uploadToBase64 middleware
    updated_by: Joi.string().uuid().optional(),
  }).xor('appointment_guest_id', 'patient_id'), // Exactly one of these must be provided
};

const authenticateDevice = {
  params: Joi.object().keys({
    deviceId: Joi.string().required(),
  }),
  body: Joi.object().keys({
    fingerprint_data: Joi.string().min(10).max(500).required(),
  }),
};

module.exports = {
  getDeviceSetting,
  getDeviceTemplate,
  fetchGuestByPin,
  getPatientByAppointmentAndPhone,
  getOutpatientDetails,
  performPatientGuestCheckin,
  getInpatientAppointmentDetails,
  storeGuestSignature,
  authenticateDevice,
};
