const express = require("express");
const validate = require("../middlewares/validate");
const { DeviceGroupValidation } = require("../validations");
const { DeviceGroupController } = require("../controllers");
const auth = require("../middlewares/auth");

const router = express.Router({ mergeParams: true });

/**
 * @swagger
 * tags:
 *   name: Facility Manager
 *   description: Device group management operations
 */

/**
 * @swagger
 * /facility/device-groups/{kioskGroupId}:
 *   get:
 *     summary: Get all device group settings for a kiosk group (paginated)
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kioskGroupId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group ID for which to fetch device group settings.
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number (default is 1).
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Number of records per page (default is 10).
 *     responses:
 *       200:
 *         description: Paginated list of device group settings with kiosk setting details.
 *         content:
 *           application/json:
 *             example:
 *               totalItems: 15
 *               totalPages: 2
 *               currentPage: 1
 *               data:
 *                 - kiosk_group_setting_id: "64b8f0e2d123e4567890abcd"
 *                   config_value: "true"
 *                   kiosk_group:
 *                     name: "Main Lobby Kiosks"
 *                   kiosk_setting:
 *                     config_key: "show_nda"
 *                     name: "Show NDA"
 *                     description: "Display NDA agreement on kiosk"
 *                     config_group: "display"
 *                     default_config_value: "true"
 */
router.get("/", auth("view_device_groups"), validate(DeviceGroupValidation.kioskGroup), DeviceGroupController.index);

/**
 * @swagger
 * /facility/device-groups/{kioskGroupId}/{kioskGroupSettingId}:
 *   get:
 *     summary: Get a device group setting by ID for a kiosk group
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kioskGroupId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group ID.
 *       - in: path
 *         name: kioskGroupSettingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group setting ID.
 *     responses:
 *       200:
 *         description: Device group setting details with kiosk setting information.
 *         content:
 *           application/json:
 *             example:
 *               kiosk_group_setting_id: "64b8f0e2d123e4567890abcd"
 *               config_value: "true"
 *               kiosk_group:
 *                 name: "Main Lobby Kiosks"
 *               kiosk_setting:
 *                 config_key: "show_nda"
 *                 name: "Show NDA"
 *                 description: "Display NDA agreement on kiosk"
 *                 config_group: "display"
 *                 default_config_value: "true"
 *       404:
 *         description: Device group setting not found.
 */
router.get(
  "/:kioskGroupSettingId",
  auth("device_group_details"),
  validate(DeviceGroupValidation.kioskGroupSetting),
  DeviceGroupController.show
);

/**
 * @swagger
 * /facility/device-groups/{kioskGroupId}:
 *   post:
 *     summary: Create a new device group setting for a kiosk group
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kioskGroupId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group ID where the setting will be created.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             kiosk_setting_id: "74b8f0e2d123e4567890abcd"
 *             config_value: "true"
 *     responses:
 *       201:
 *         description: Device group setting created successfully.
 */
router.post("/", auth("create_device_group_setting"), validate(DeviceGroupValidation.create), DeviceGroupController.create);

/**
 * @swagger
 * /facility/device-groups/{kioskGroupId}/{kioskGroupSettingId}:
 *   patch:
 *     summary: Update a device group setting's details
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kioskGroupId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group ID.
 *       - in: path
 *         name: kioskGroupSettingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group setting ID.
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           example:
 *             config_value: "false"
 *     responses:
 *       200:
 *         description: Device group setting updated successfully.
 */
router.patch("/:kioskGroupSettingId", auth("edit_device_group_setting"), validate(DeviceGroupValidation.update), DeviceGroupController.update);

/**
 * @swagger
 * /facility/device-groups/{kioskGroupId}/{kioskGroupSettingId}:
 *   delete:
 *     summary: Delete a device group setting by ID
 *     tags: [Facility Manager]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: kioskGroupId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group ID
 *       - in: path
 *         name: kioskGroupSettingId
 *         required: true
 *         schema:
 *           type: string
 *         description: The kiosk group setting ID to delete
 *     responses:
 *       200:
 *         description: Device group setting deleted successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Device group setting deleted successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     kiosk_group_setting_id:
 *                       type: string
 *                       example: "64b8f0e2d123e4567890abcd"
 *       404:
 *         description: Device group setting not found for the given kiosk group
 *       401:
 *         description: Unauthorized
 */
router.delete("/:kioskGroupSettingId", auth("delete_device_group_setting"), validate(DeviceGroupValidation.remove), DeviceGroupController.remove);

module.exports = router;
