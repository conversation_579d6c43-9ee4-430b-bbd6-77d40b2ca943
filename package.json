{"name": "api", "version": "1.0.0", "main": "server.js", "scripts": {"start": "nodemon server.js --env-file .env.dev", "dev": "nodemon server.js --env-file .env.local", "db": "node scripts/dbSync.js", "db:refresh": "node scripts/dbSync.js refresh", "db:reset": "node scripts/db-reset.js", "db:reset:all": "node scripts/db-reset.js --all", "db:reset:parallel": "node scripts/db-reset.js --all --parallel", "db:reset:local": "node scripts/db-reset.js --db local", "db:reset:core": "node scripts/db-reset.js --db core"}, "sequelize": {"config": "config/database.js", "models-path": "models", "seeders-path": "seeders", "migrations-path": "migrations"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"@faker-js/faker": "^9.6.0", "@keyv/memcache": "^2.0.1", "@keyv/redis": "^4.3.2", "amqplib": "^0.10.5", "bcrypt": "^5.1.1", "cache-manager": "^6.4.1", "cors": "^2.8.5", "crypto-js": "^4.2.0", "csurf": "^1.10.0", "dotenv": "^16.4.7", "express": "^4.21.2", "express-rate-limit": "^7.5.0", "express-validator": "^7.2.1", "helmet": "^8.0.0", "http-status": "^2.1.0", "joi": "^17.13.3", "json-rules-engine": "^7.3.1", "jsonwebtoken": "^9.0.2", "keyv": "^5.3.2", "lodash": "^4.17.21", "moment": "^2.30.1", "morgan": "^1.10.0", "multer": "^2.0.0", "passport": "^0.7.0", "passport-azure-ad": "^4.3.5", "passport-jwt": "^4.0.1", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "passport-openidconnect": "^0.1.2", "passport-saml": "^3.2.4", "pg": "^8.13.1", "qrcode": "^1.5.4", "rate-limiter-flexible": "^5.0.5", "redis": "^4.7.0", "sequelize": "^6.37.5", "sharp": "^0.34.1", "swagger-jsdoc": "^6.2.8", "swagger-ui-express": "^5.0.1", "uuid": "^11.0.5", "winston": "^3.17.0", "winston-daily-rotate-file": "^5.0.0"}, "devDependencies": {"chai": "^5.1.2", "jest": "^29.7.0", "mocha": "^11.1.0", "nodemon": "^3.1.9", "sinon": "^19.0.2", "supertest": "^7.0.0"}}